 "/system/v1/tham-so-he-thong": {
      "post": {
        "tags": [
          "14. Tham số hệ thống
        ],
        "parameters": [
          {
            "name": "x-language",
            "in": "header",
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateThamSoHeThongModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateThamSoHeThongModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/CreateThamSoHeThongModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/UnitResponseObject"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UnitResponseObject"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/UnitResponseObject"
                }
              }
            }
          }
        }
      }
    },
    "/system/v1/tham-so-he-thong/{idPh}/{id}": {
      "get": {
        "tags": [
          "14. Tham sá»‘ há»‡ thá»‘ng"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "idPh",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "x-language",
            "in": "header",
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ThamSoHeThongModelResponseObject"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ThamSoHeThongModelResponseObject"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ThamSoHeThongModelResponseObject"
                }
              }
            }
          }
        }
      },
      "delete": {
        "tags": [
          "14. Tham sá»‘ há»‡ thá»‘ng"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "idPh",
            "in": "path",
            "required": true,
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "x-language",
            "in": "header",
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/UnitResponseObject"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UnitResponseObject"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/UnitResponseObject"
                }
              }
            }
          }
        }
      }
    },
    "/system/v1/tham-so-he-thong/{id}": {
      "put": {
        "tags": [
          "14. Tham sá»‘ há»‡ thá»‘ng"
        ],
        "parameters": [
          {
            "name": "id",
            "in": "path",
            "required": true,
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "x-language",
            "in": "header",
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateThamSoHeThongModel"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateThamSoHeThongModel"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateThamSoHeThongModel"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/UnitResponseObject"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/UnitResponseObject"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/UnitResponseObject"
                }
              }
            }
          }
        }
      }
    },
    "/system/v1/tham-so-he-thong/filter": {
      "post": {
        "tags": [
          "14. Tham sá»‘ há»‡ thá»‘ng"
        ],
        "parameters": [
          {
            "name": "x-language",
            "in": "header",
            "schema": {
              "type": "string"
            }
          }
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/ThamSoHeThongQueryFilter"
              }
            },
            "text/json": {
              "schema": {
                "$ref": "#/components/schemas/ThamSoHeThongQueryFilter"
              }
            },
            "application/*+json": {
              "schema": {
                "$ref": "#/components/schemas/ThamSoHeThongQueryFilter"
              }
            }
          }
        },
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ThamSoHeThongModelPaginationListResponseObject"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ThamSoHeThongModelPaginationListResponseObject"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ThamSoHeThongModelPaginationListResponseObject"
                }
              }
            }
          }
        }
      }
    },
    "/system/v1/tham-so-he-thong/for-combobox": {
      "get": {
        "tags": [
          "14. Tham sá»‘ há»‡ thá»‘ng"
        ],
        "parameters": [
          {
            "name": "count",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32",
              "default": 0
            }
          },
          {
            "name": "ts",
            "in": "query",
            "schema": {
              "type": "string",
              "default": ""
            }
          },
          {
            "name": "idPh",
            "in": "query",
            "schema": {
              "type": "integer",
              "format": "int32"
            }
          },
          {
            "name": "nhomThamSo",
            "in": "query",
            "schema": {
              "type": "string",
              "default": ""
            }
          },
          {
            "name": "x-language",
            "in": "header",
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ThamSoHeThongSelectItemModelListResponseObject"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ThamSoHeThongSelectItemModelListResponseObject"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ThamSoHeThongSelectItemModelListResponseObject"
                }
              }
            }
          }
        }
      }
    },
    "/system/v1/tham-so-he-thong/by-phan-he": {
      "get": {
        "tags": [
          "14. Tham sá»‘ há»‡ thá»‘ng"
        ],
        "parameters": [
          {
            "name": "phanHe",
            "in": "query",
            "schema": {
              "type": "string"
            }
          },
          {
            "name": "x-language",
            "in": "header",
            "schema": {
              "type": "string"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "Success",
            "content": {
              "text/plain": {
                "schema": {
                  "$ref": "#/components/schemas/ThamSoHeThongModelListResponseObject"
                }
              },
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/ThamSoHeThongModelListResponseObject"
                }
              },
              "text/json": {
                "schema": {
                  "$ref": "#/components/schemas/ThamSoHeThongModelListResponseObject"
                }
              }
            }
          }
        }
      }
    },