import { Component, EventEmitter, Inject, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, RequiredValidator, Validators } from '@angular/forms';
import ClassicEditor from '@ckeditor/ckeditor5-build-classic';
import { I18NService } from '@core';
import { ACLService } from '@delon/acl';
import { ALAIN_I18N_TOKEN } from '@delon/theme';
import { log } from '@delon/util';
import { ButtonModel } from '@model';
import { cleanForm, EVENT_TYPE, FORM_TYPE } from '@util';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Subscription } from 'rxjs';
import { ThamSoHeThongApiService } from 'src/app/services/api/tham-so-he-thong-api.service';
import { WorkflowApiService } from 'src/app/services/api/workflow-api.service';

@Component({
  selector: 'app-workflow-map-function-item',
  templateUrl: './workflow-map-function-item.component.html',
  styleUrls: ['./workflow-map-function-item.component.less']
})
export class WorkflowMapFunctionItemComponent implements OnInit {
  @Input() type = 'add';
  @Input() item: any;
  @Input() isVisible = false;
  @Input() option: any;
  @Output() readonly eventEmmit = new EventEmitter<any>();

  form: FormGroup;

  tittle = '';

  isLoading = false;

  btnSave: ButtonModel;
  btnCancel: ButtonModel;
  btnEdit: ButtonModel;

  public Editor = ClassicEditor;

  config = {
    toolbar: {
      shouldNotGroupWhenFull: true
    }
  };

  lstQuyTrinh: any;

  constructor(
    private fb: FormBuilder,
    private messageService: NzMessageService,
    private thamSoHeThongApiService: ThamSoHeThongApiService,
    private workflowApiService: WorkflowApiService,
    @Inject(ALAIN_I18N_TOKEN) private i18n: I18NService,
    private aclService: ACLService
  ) {
    this.btnSave = {
      title: this.i18n.fanyi('app.common.button.save'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.save();
      }
    };
    this.btnCancel = {
      title: this.i18n.fanyi('app.common.button.close'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.handleCancel();
      }
    };
    this.btnEdit = {
      title: this.i18n.fanyi('app.common.button.edit'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: ($event: any) => {
        this.updateFormToEdit();
      }
    };
    this.form = this.fb.group({
      phanHe: [''],
      tenThamSo: [''],
      ghiChu: [''],
      giaTri: [null, [Validators.required]],
      active: [false],
      idPh: [null],
      nhomThamSo: [''],
      idThamSo: ['']
    });
  }

  onEditorReady(editor: any): void {
    log('Editor is ready to use!', editor);
  }

  handleCancel(): void {
    this.isVisible = false;
    this.eventEmmit.emit({ type: EVENT_TYPE.CLOSE });
  }

  ngOnInit(): void {
    this.initRightOfUser();
    this.getComboboxWorkflow();
  }

  initRightOfUser(): void {}

  //#region Update-form-type

  updateFormToEdit(): void {
    this.form.get('phanHe')?.disable();
    this.form.get('tenThamSo')?.disable();
    this.form.get('giaTri')?.enable();
    this.form.get('active')?.enable();
  }

  updateFormData(item: any): void {
    this.form.get('phanHe')?.setValue(item?.phanHe);
    this.form.get('tenThamSo')?.setValue(item?.tenThamSo);
    this.form.get('giaTri')?.setValue(item?.giaTri);
    this.form.get('active')?.setValue(item?.active);
    this.form.get('idPh')?.setValue(item?.idPh);
    this.form.get('nhomThamSo')?.setValue(item?.nhomThamSo);
    this.form.get('idThamSo')?.setValue(item?.idThamSo);
  }

  clearFormData(item: any): void {}

  //#endregion Update-form-type

  public initData(data: any, type: any = null, option: any = {}): void {
    this.form.reset();
    this.isLoading = false;
    this.item = data;
    this.getDataInfo(data?.idThamSo, data?.idPh);
    this.type = type;
    this.option = option;
  }

  closeModalReloadData(): void {
    this.isVisible = false;
    this.eventEmmit.emit({ type: EVENT_TYPE.SUCCESS });
  }

  getDataInfo(idThamSo: any, idPh: any): Subscription | undefined {
    this.isLoading = true;
    const rs = this.thamSoHeThongApiService.getById(idPh, idThamSo).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (res.data === null || res.data === undefined) {
          this.messageService.error(`${res.message}`);
          return;
        }
        this.item = res.data;
        this.updateFormToEdit();
        this.updateFormData(res.data);
      },
      error: (err: any) => {
        this.isLoading = false;
      },
      complete: () => {
        this.isLoading = false;
      }
    });
    return rs;
  }

  save(): Subscription | undefined {
    this.isLoading = true;
    const rs = this.thamSoHeThongApiService.update(this.item.idThamSo, { ...this.form.value }).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        this.messageService.success(res?.message);
        this.closeModalReloadData();
      },
      error: (err: any) => {
        this.messageService.success(err?.error?.message);
        this.isLoading = false;
      }
    });
    return rs;
  }

  getComboboxWorkflow() {
    this.workflowApiService.getCombobox().subscribe({
      next: (res: any) => {
        this.lstQuyTrinh = res.data;
      },
      error: (err: any) => {
        this.messageService.success(err?.error?.message);
      }
    });
  }
}
