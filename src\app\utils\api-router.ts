export const applicationRouter = {
  create: `/api/v1/system-application`,
  createMany: `/api/v1/system-application/create-many`,
  update: `/api/v1/system-application`,
  delete: `/api/v1/system-application`,
  getById: `/api/v1/system-application/`,
  getFilter: `/api/v1/system-application/filter`,
  getAll: `/api/v1/system-application/all`,
  getCombobox: `/api/v1/system-application/for-combobox`
};

export const emailTemplateRouter = {
  create: `/system/v1/email-template`,
  update: `/system/v1/email-template/`,
  delete: `/system/v1/email-template/`,
  getById: `/system/v1/email-template/`,
  getFilter: `/system/v1/email-template/filter`,
  getCombobox: `/system/v1/email-template/for-combobox`
};

export const workflowRouter = {
  create: `/system/v1/workflow`,
  update: `/system/v1/workflow/`,
  delete: `/system/v1/workflow/`,
  getById: `/system/v1/workflow/`,
  getHistoryById: `/system/v1/workflow/history/`,
  getFilter: `/system/v1/workflow/filter`,
  getCombobox: `/system/v1/workflow/for-combobox`
};

export const mucHuongBhytRouter = {
  create: `/system/v1/muc-huong-bhyt`,
  update: `/system/v1/muc-huong-bhyt/`,
  delete: `/system/v1/muc-huong-bhyt/`,
  getById: `/system/v1/muc-huong-bhyt/`,
  getFilter: `/system/v1/muc-huong-bhyt/filter`,
  getCombobox: `/system/v1/muc-huong-bhyt/for-combobox`
};

export const vungRouter = {
  create: `/system/v1/vung`,
  update: `/system/v1/vung/`,
  delete: `/system/v1/vung/`,
  getById: `/system/v1/vung/`,
  getFilter: `/system/v1/vung/filter`,
  getCombobox: `/system/v1/vung/for-combobox`
};

export const benhVienRouter = {
  create: `/system/v1/benh-vien`,
  update: `/system/v1/benh-vien/`,
  delete: `/system/v1/benh-vien/`,
  getById: `/system/v1/benh-vien/`,
  getFilter: `/system/v1/benh-vien/filter`,
  getCombobox: `/system/v1/benh-vien/for-combobox`
};

export const phuongAnRouter = {
  create: `/system/v1/phuong-an`,
  update: `/system/v1/phuong-an/`,
  delete: `/system/v1/phuong-an/`,
  getById: `/system/v1/phuong-an/`,
  getFilter: `/system/v1/phuong-an/filter`,
  getCombobox: `/system/v1/phuong-an/for-combobox`
};

export const phuongThucDongRouter = {
  create: `/system/v1/phuong-thuc-dong`,
  update: `/system/v1/phuong-thuc-dong/`,
  delete: `/system/v1/phuong-thuc-dong/`,
  getById: `/system/v1/phuong-thuc-dong/`,
  getFilter: `/system/v1/phuong-thuc-dong/filter`,
  getCombobox: `/system/v1/phuong-thuc-dong/for-combobox`
};

export const systemLogRouter = {
  getFilter: `/system/v1/system-log/filter`,
  getAllActionCodeForCombobox: `/system/v1/system-log/get-all-action-code-for-combobox`
};
export const emailLogRouter = {
  getFilter: `/system/v1/send-mail-log/filter`
};
export const forgotPasswordLogRouter = {
  getFilter: `/system/v1/forgot-password-log/filter`
};
export const userRouter = {
  updateRoleOfUser: `/system/v1/user/update-role-by-user`,
  updateLockStatusOfUser: `/system/v1/user/update-lock-status?id=`,
  getUserRoleByUserId: `/system/v1/user/get-role-by-user?userId=`,
  getFilter: `/system/v1/user/filter`,
  getListCombobox: `/system/v1/user/for-combobox`,
  getUserPermission: `/system/v1/user/get-permission-current-user`,
  sendMailForgotPassword: `/system/v1/user/send-mail-forgot-password`,
  create: `/system/v1/user`,
  update: `/system/v1/user/`,
  delete: `/system/v1/user/`,
  getById: `/system/v1/user/`,
  ganLopQuanLy: `/system/v1/user/assign-class`,
  xoaLopQuanLy: `/system/v1/user/delete-assign-class/`,
  getLopQuanLybyIdUser: `/system/v1/user/access-he/`
};
export const roleRouter = {
  create: `/system/v1/role`,
  update: `/system/v1/role/`,
  delete: `/system/v1/role/`,
  getById: `/system/v1/role/`,
  getFilter: `/system/v1/role/filter`,
  getAll: `/system/v1/role/all`,
  getListCombobox: `/system/v1/role/for-combobox`,
  getListUserIdByRole: `/system/v1/role/get-user-by-role?roleId=`,
  getListPermissionIdByRole: `/system/v1/role/get-permission-by-role?roleId=`,
  updateUserByRole: `/system/v1/role/update-user-by-role`,
  updatePermissionByRole: `/system/v1/role/update-permission-by-role`
};
export const permissionRouter = {
  getListCombobox: `/system/v1/permission/for-combobox`
};

export const phanHeRouter = {
  getAuthorizedForUser: `/system/v1/phan-he/authorized-for-users`,
  getListCombobox: `/system/v1/phan-he/for-combobox`
};

export const hoSoNhanSuRouter = {
  getCombobox: '/hrm/v1/ho-so-chinh/for-combobox'
};

export const heDaoTaoRouter = {
  create: `/system/v1/he`,
  update: `/system/v1/he/`,
  delete: `/system/v1/he/`,
  getById: `/system/v1/he/`,
  getFilter: `/system/v1/he/filter`,
  getCombobox: `/system/v1/he/for-combobox`
};

export const tonGiaoRouter = {
  create: `/system/v1/ton-giao`,
  update: `/system/v1/ton-giao/`,
  delete: `/system/v1/ton-giao/`,
  getById: `/system/v1/ton-giao/`,
  getFilter: `/system/v1/ton-giao/filter`,
  getCombobox: `/system/v1/ton-giao/for-combobox`
};

export const chiTieuTuyenSinhRouter = {
  create: `/system/v1/chi-tieu-tuyen-sinh`,
  update: `/system/v1/chi-tieu-tuyen-sinh/`,
  delete: `/system/v1/chi-tieu-tuyen-sinh/`,
  getById: `/system/v1/chi-tieu-tuyen-sinh/`,
  getFilter: `/system/v1/chi-tieu-tuyen-sinh/filter`
};
export const quocTichRouter = {
  create: `/system/v1/quoc-tich`,
  update: `/system/v1/quoc-tich/`,
  delete: `/system/v1/quoc-tich/`,
  getById: `/system/v1/quoc-tich/`,
  getFilter: `/system/v1/quoc-tich/filter`,
  getCombobox: `/system/v1/quoc-tich/for-combobox`
};

export const danTocRouter = {
  create: `/system/v1/dan-toc`,
  update: `/system/v1/dan-toc/`,
  delete: `/system/v1/dan-toc/`,
  getById: `/system/v1/dan-toc/`,
  getFilter: `/system/v1/dan-toc/filter`,
  getCombobox: `/system/v1/dan-toc/for-combobox`
};

export const gioiTinhRouter = {
  create: `/system/v1/gioi-tinh`,
  update: `/system/v1/gioi-tinh/`,
  delete: `/system/v1/gioi-tinh/`,
  getById: `/system/v1/gioi-tinh/`,
  getFilter: `/system/v1/gioi-tinh/filter`,
  getCombobox: `/system/v1/gioi-tinh/for-combobox`
};

export const hocViRouter = {
  create: `/system/v1/hoc-vi`,
  update: `/system/v1/hoc-vi/`,
  delete: `/system/v1/hoc-vi/`,
  getById: `/system/v1/hoc-vi/`,
  getFilter: `/system/v1/hoc-vi/filter`,
  getCombobox: `/system/v1/hoc-vi/for-combobox`
};

export const hocHamRouter = {
  create: `/system/v1/hoc-ham`,
  update: `/system/v1/hoc-ham/`,
  delete: `/system/v1/hoc-ham/`,
  getById: `/system/v1/hoc-ham/`,
  getFilter: `/system/v1/hoc-ham/filter`,
  getCombobox: `/system/v1/hoc-ham/for-combobox`
};

export const khoaRouter = {
  create: `/system/v1/khoa`,
  update: `/system/v1/khoa/`,
  delete: `/system/v1/khoa/`,
  getById: `/system/v1/khoa/`,
  getFilter: `/system/v1/khoa/filter`,
  getCombobox: `/system/v1/khoa/for-combobox`
};

export const lopRouter = {
  getFilter: `/system/v1/lop/filter`
};

export const nganhRouter = {
  create: `/system/v1/nganh`,
  update: `/system/v1/nganh/`,
  delete: `/system/v1/nganh/`,
  getById: `/system/v1/nganh/`,
  getFilter: `/system/v1/nganh/filter`,
  getCombobox: `/system/v1/nganh/for-combobox`
};

export const chuyenNganhRouter = {
  create: `/system/v1/chuyen-nganh`,
  update: `/system/v1/chuyen-nganh/`,
  delete: `/system/v1/chuyen-nganh/`,
  getById: `/system/v1/chuyen-nganh/`,
  getFilter: `/system/v1/chuyen-nganh/filter`,
  getCombobox: `/system/v1/chuyen-nganh/for-combobox`
};

export const tinhRouter = {
  create: `/system/v1/tinh`,
  update: `/system/v1/tinh/`,
  delete: `/system/v1/tinh/`,
  getById: `/system/v1/tinh/`,
  getFilter: `/system/v1/tinh/filter`,
  getCombobox: `/system/v1/tinh/for-combobox`
};

export const huyenRouter = {
  create: `/system/v1/huyen`,
  update: `/system/v1/huyen/`,
  delete: `/system/v1/huyen/`,
  getById: `/system/v1/huyen/`,
  getFilter: `/system/v1/huyen/filter`,
  getCombobox: `/system/v1/huyen/for-combobox`
};

export const khuVucRouter = {
  create: `/system/v1/khu-vuc`,
  update: `/system/v1/khu-vuc/`,
  delete: `/system/v1/khu-vuc/`,
  getById: `/system/v1/khu-vuc/`,
  getFilter: `/system/v1/khu-vuc/filter`,
  getCombobox: `/system/v1/khu-vuc/for-combobox`
};

export const nhomDoiTuongRouter = {
  create: `/system/v1/nhom-doi-tuong`,
  update: `/system/v1/nhom-doi-tuong/`,
  delete: `/system/v1/nhom-doi-tuong/`,
  getById: `/system/v1/nhom-doi-tuong/`,
  getFilter: `/system/v1/nhom-doi-tuong/filter`,
  getCombobox: `/system/v1/nhom-doi-tuong/for-combobox`
};

export const doiTuongRouter = {
  create: `/system/v1/doi-tuong`,
  update: `/system/v1/doi-tuong/`,
  delete: `/system/v1/doi-tuong/`,
  getById: `/system/v1/doi-tuong/`,
  getFilter: `/system/v1/doi-tuong/filter`,
  getCombobox: `/system/v1/doi-tuong/for-combobox`
};

export const doiTuongHocBongRouter = {
  create: `/system/v1/doi-tuong-hoc-bong`,
  update: `/system/v1/doi-tuong-hoc-bong/`,
  delete: `/system/v1/doi-tuong-hoc-bong/`,
  getById: `/system/v1/doi-tuong-hoc-bong/`,
  getFilter: `/system/v1/doi-tuong-hoc-bong/filter`,
  getCombobox: `/system/v1/doi-tuong-hoc-bong/for-combobox`
};

export const capKhenThuongKyLuatRouter = {
  create: `/system/v1/cap-khen-thuong-ky-luat`,
  update: `/system/v1/cap-khen-thuong-ky-luat/`,
  delete: `/system/v1/cap-khen-thuong-ky-luat/`,
  getById: `/system/v1/cap-khen-thuong-ky-luat/`,
  getFilter: `/system/v1/cap-khen-thuong-ky-luat/filter`,
  getCombobox: `/system/v1/cap-khen-thuong-ky-luat/for-combobox`
};

export const loaiKhenThuongRouter = {
  create: `/system/v1/loai-khen-thuong`,
  update: `/system/v1/loai-khen-thuong/`,
  delete: `/system/v1/loai-khen-thuong/`,
  getById: `/system/v1/loai-khen-thuong/`,
  getFilter: `/system/v1/loai-khen-thuong/filter`,
  getCombobox: `/system/v1/loai-khen-thuong/for-combobox`
};

export const hanhViRouter = {
  create: `/system/v1/hanh-vi-ky-luat`,
  update: `/system/v1/hanh-vi-ky-luat/`,
  delete: `/system/v1/hanh-vi-ky-luat/`,
  getById: `/system/v1/hanh-vi-ky-luat/`,
  getFilter: `/system/v1/hanh-vi-ky-luat/filter`,
  getCombobox: `/system/v1/hanh-vi-ky-luat/for-combobox`
};

export const xuLyRouter = {
  create: `/system/v1/xu-ly-ky-luat`,
  update: `/system/v1/xu-ly-ky-luat/`,
  delete: `/system/v1/xu-ly-ky-luat/`,
  getById: `/system/v1/xu-ly-ky-luat/`,
  getFilter: `/system/v1/xu-ly-ky-luat/filter`,
  getCombobox: `/system/v1/xu-ly-ky-luat/for-combobox`
};

export const chucDanhRouter = {
  create: `/system/v1/chuc-danh`,
  update: `/system/v1/chuc-danh/`,
  delete: `/system/v1/chuc-danh/`,
  getById: `/system/v1/chuc-danh/`,
  getFilter: `/system/v1/chuc-danh/filter`,
  getCombobox: `/system/v1/chuc-danh/for-combobox`
};

export const xepLoaiRenLuyenRouter = {
  create: `/system/v1/xep-loai-ren-luyen`,
  update: `/system/v1/xep-loai-ren-luyen/`,
  delete: `/system/v1/xep-loai-ren-luyen/`,
  getById: `/system/v1/xep-loai-ren-luyen/`,
  getFilter: `/system/v1/xep-loai-ren-luyen/filter`,
  getCombobox: `/system/v1/xep-loai-ren-luyen/for-combobox`
};
export const loaiRenLuyenRouter = {
  create: `/system/v1/loai-ren-luyen`,
  update: `/system/v1/loai-ren-luyen/`,
  delete: `/system/v1/loai-ren-luyen/`,
  getById: `/system/v1/loai-ren-luyen/`,
  getFilter: `/system/v1/loai-ren-luyen/filter`,
  getCombobox: `/system/v1/loai-ren-luyen/for-combobox`
};

export const loaiThanhPhanDiemRouter = {
  create: `/system/v1/loai-thanh-phan-diem`,
  update: `/system/v1/loai-thanh-phan-diem/`,
  delete: `/system/v1/loai-thanh-phan-diem/`,
  getById: `/system/v1/loai-thanh-phan-diem/`,
  getFilter: `/system/v1/loai-thanh-phan-diem/filter`,
  getCombobox: `/system/v1/loai-thanh-phan-diem/for-combobox`
};

export const xepLoaiHocTapThangDiem10Router = {
  create: `/system/v1/xep-loai-hoc-tap-thang-diem-10`,
  update: `/system/v1/xep-loai-hoc-tap-thang-diem-10/`,
  delete: `/system/v1/xep-loai-hoc-tap-thang-diem-10/`,
  getById: `/system/v1/xep-loai-hoc-tap-thang-diem-10/`,
  getFilter: `/system/v1/xep-loai-hoc-tap-thang-diem-10/filter`,
  getCombobox: `/system/v1/xep-loai-hoc-tap-thang-diem-10/for-combobox`
};

export const xepHangHocLucRouter = {
  create: `/system/v1/xep-hang-hoc-luc`,
  update: `/system/v1/xep-hang-hoc-luc/`,
  delete: `/system/v1/xep-hang-hoc-luc/`,
  getById: `/system/v1/xep-hang-hoc-luc/`,
  getFilter: `/system/v1/xep-hang-hoc-luc/filter`,
  getCombobox: `/system/v1/xep-hang-hoc-luc/for-combobox`
};

export const xepHangNamDaoTaoRouter = {
  create: `/system/v1/xep-hang-nam-dao-tao`,
  update: `/system/v1/xep-hang-nam-dao-tao/`,
  delete: `/system/v1/xep-hang-nam-dao-tao/`,
  getById: `/system/v1/xep-hang-nam-dao-tao/`,
  getFilter: `/system/v1/xep-hang-nam-dao-tao/filter`,
  getCombobox: `/system/v1/xep-hang-nam-dao-tao/for-combobox`
};

export const xepLoaiHocTapThang4Router = {
  create: `/system/v1/xep-loai-hoc-tap-thang-4`,
  update: `/system/v1/xep-loai-hoc-tap-thang-4/`,
  delete: `/system/v1/xep-loai-hoc-tap-thang-4/`,
  getById: `/system/v1/xep-loai-hoc-tap-thang-4/`,
  getFilter: `/system/v1/xep-loai-hoc-tap-thang-4/filter`,
  getCombobox: `/system/v1/xep-loai-hoc-tap-thang-4/for-combobox`
};

export const xepLoaiTotNghiepThang4Router = {
  create: `/system/v1/xep-loai-tot-nghiep-thang-4`,
  update: `/system/v1/xep-loai-tot-nghiep-thang-4/`,
  delete: `/system/v1/xep-loai-tot-nghiep-thang-4/`,
  getById: `/system/v1/xep-loai-tot-nghiep-thang-4/`,
  getFilter: `/system/v1/xep-loai-tot-nghiep-thang-4/filter`,
  getCombobox: `/system/v1/xep-loai-tot-nghiep-thang-4/for-combobox`
};

export const xepLoaiTotNghiepThang10Router = {
  create: `/system/v1/xep-hang-tot-nghiep-thang-diem-10`,
  update: `/system/v1/xep-hang-tot-nghiep-thang-diem-10/`,
  delete: `/system/v1/xep-hang-tot-nghiep-thang-diem-10/`,
  getById: `/system/v1/xep-hang-tot-nghiep-thang-diem-10/`,
  getFilter: `/system/v1/xep-hang-tot-nghiep-thang-diem-10/filter`,
  getCombobox: `/system/v1/xep-hang-tot-nghiep-thang-diem-10/for-combobox`
};

export const loaiChungChiRouter = {
  create: `/system/v1/loai-chung-chi`,
  update: `/system/v1/loai-chung-chi/`,
  delete: `/system/v1/loai-chung-chi/`,
  getById: `/system/v1/loai-chung-chi/`,
  getFilter: `/system/v1/loai-chung-chi/filter`,
  getCombobox: `/system/v1/loai-chung-chi/for-combobox`
};

export const nhomChungChiRouter = {
  create: `/system/v1/nhom-chung-chi`,
  update: `/system/v1/nhom-chung-chi/`,
  delete: `/system/v1/nhom-chung-chi/`,
  getById: `/system/v1/nhom-chung-chi/`,
  getFilter: `/system/v1/nhom-chung-chi/filter`,
  getCombobox: `/system/v1/nhom-chung-chi/for-combobox`
};

export const xepLoaiChungChiRouter = {
  create: `/system/v1/xep-loai-chung-chi`,
  update: `/system/v1/xep-loai-chung-chi/`,
  delete: `/system/v1/xep-loai-chung-chi/`,
  getById: `/system/v1/xep-loai-chung-chi/`,
  getFilter: `/system/v1/xep-loai-chung-chi/filter`,
  getCombobox: `/system/v1/xep-loai-chung-chi/for-combobox`
};
export const loaiGiayToRouter = {
  create: `/system/v1/loai-giay-to`,
  update: `/system/v1/loai-giay-to/`,
  delete: `/system/v1/loai-giay-to/`,
  getById: `/system/v1/loai-giay-to/`,
  getFilter: `/system/v1/loai-giay-to/filter`,
  getCombobox: `/system/v1/loai-giay-to/for-combobox`
};
export const hocKyDangKyRouter = {
  create: `/system/v1/hoc-ky-dang-ky`,
  update: `/system/v1/hoc-ky-dang-ky/`,
  delete: `/system/v1/hoc-ky-dang-ky/`,
  getById: `/system/v1/hoc-ky-dang-ky/`,
  getFilter: `/system/v1/hoc-ky-dang-ky/filter`,
  getCombobox: `/system/v1/hoc-ky-dang-ky/for-combobox`
};
export const phuongNgoaiTruRouter = {
  create: `/system/v1/phuong`,
  update: `/system/v1/phuong/`,
  delete: `/system/v1/phuong/`,
  getById: `/system/v1/phuong/`,
  getFilter: `/system/v1/phuong/filter`,
  getCombobox: `/system/v1/phuong/for-combobox`
};
export const xaRouter = {
  create: `/system/v1/xa`,
  update: `/system/v1/xa/`,
  delete: `/system/v1/xa/`,
  getById: `/system/v1/xa/`,
  getFilter: `/system/v1/xa/filter`,
  getCombobox: `/system/v1/xa/for-combobox`
};
export const hinhThucHocRouter = {
  create: `/system/v1/hinh-thuc-hoc`,
  update: `/system/v1/hinh-thuc-hoc/`,
  delete: `/system/v1/hinh-thuc-hoc/`,
  getById: `/system/v1/hinh-thuc-hoc/`,
  getFilter: `/system/v1/hinh-thuc-hoc/filter`,
  getCombobox: `/system/v1/hinh-thuc-hoc/for-combobox`
};
export const hinhThucThiRouter = {
  create: `/system/v1/hinh-thuc-thi/`,
  update: `/system/v1/hinh-thuc-thi/`,
  delete: `/system/v1/hinh-thuc-thi/`,
  getById: `/system/v1/hinh-thuc-thi/`,
  getFilter: `/system/v1/hinh-thuc-thi/filter`,
  getCombobox: `/system/v1/hinh-thuc-thi/for-combobox`
};
export const xepLoaiHocBongRouter = {
  create: `/system/v1/xep-loai-hoc-bong`,
  update: `/system/v1/xep-loai-hoc-bong/`,
  delete: `/system/v1/xep-loai-hoc-bong/`,
  getById: `/system/v1/xep-loai-hoc-bong/`,
  getFilter: `/system/v1/xep-loai-hoc-bong/filter`,
  getCombobox: `/system/v1/xep-loai-hoc-bong/for-combobox`
};
export const toaNhaRouter = {
  create: `/system/v1/toa-nha`,
  update: `/system/v1/toa-nha/`,
  delete: `/system/v1/toa-nha/`,
  getById: `/system/v1/toa-nha/`,
  getFilter: `/system/v1/toa-nha/filter`,
  getCombobox: `/system/v1/toa-nha/for-combobox`
};
export const doiTuongHocPhiRouter = {
  create: `/system/v1/doi-tuong-hoc-phi`,
  update: `/system/v1/doi-tuong-hoc-phi/`,
  delete: `/system/v1/doi-tuong-hoc-phi/`,
  getById: `/system/v1/doi-tuong-hoc-phi/`,
  getFilter: `/system/v1/doi-tuong-hoc-phi/filter`,
  getCombobox: `/system/v1/doi-tuong-hoc-phi/for-combobox`
};
export const coSoDaoTaoRouter = {
  create: `/system/v1/co-so-dao-tao`,
  update: `/system/v1/co-so-dao-tao/`,
  delete: `/system/v1/co-so-dao-tao/`,
  getById: `/system/v1/co-so-dao-tao/`,
  getFilter: `/system/v1/co-so-dao-tao/filter`,
  getCombobox: `/system/v1/co-so-dao-tao/for-combobox`
};
export const boMonRouter = {
  create: `/system/v1/bo-mon`,
  update: `/system/v1/bo-mon/`,
  delete: `/system/v1/bo-mon/`,
  getById: `/system/v1/bo-mon/`,
  getFilter: `/system/v1/bo-mon/filter`,
  getCombobox: `/system/v1/bo-mon/for-combobox`
};
export const diemRenLuyenQuyDoiRouter = {
  create: `/system/v1/diem-ren-luyen-quy-doi`,
  update: `/system/v1/diem-ren-luyen-quy-doi/`,
  delete: `/system/v1/diem-ren-luyen-quy-doi/`,
  getById: `/system/v1/diem-ren-luyen-quy-doi/`,
  getFilter: `/system/v1/diem-ren-luyen-quy-doi/filter`,
  getCombobox: `/system/v1/diem-ren-luyen-quy-doi/for-combobox`
};
export const donViThucTapRouter = {
  create: `/system/v1/noi-thuc-tap/`,
  update: `/system/v1/noi-thuc-tap/`,
  delete: `/system/v1/noi-thuc-tap/`,
  getById: `/system/v1/noi-thuc-tap/`,
  getFilter: `/system/v1/noi-thuc-tap/filter`,
  getCombobox: `/system/v1/noi-thuc-tap/for-combobox`
};
export const loaiQuyetDinhRouter = {
  create: `/system/v1/loai-quyet-dinh`,
  update: `/system/v1/loai-quyet-dinh/`,
  delete: `/system/v1/loai-quyet-dinh/`,
  getById: `/system/v1/loai-quyet-dinh/`,
  getFilter: `/system/v1/loai-quyet-dinh/filter`,
  getCombobox: `/system/v1/loai-quyet-dinh/for-combobox`
};
export const phongBanRouter = {
  create: `/system/v1/phong`,
  update: `/system/v1/phong/`,
  delete: `/system/v1/phong/`,
  getById: `/system/v1/phong/`,
  getFilter: `/system/v1/phong/filter`,
  getCombobox: `/system/v1/phong/for-combobox`
};

export const khoiKienThucRouter = {
  create: `/system/v1/chuong-trinh-dao-tao-kien-thuc`,
  update: `/system/v1/chuong-trinh-dao-tao-kien-thuc/`,
  delete: `/system/v1/chuong-trinh-dao-tao-kien-thuc/`,
  getById: `/system/v1/chuong-trinh-dao-tao-kien-thuc/`,
  getFilter: `/system/v1/chuong-trinh-dao-tao-kien-thuc/filter`,
  getCombobox: `/system/v1/chuong-trinh-dao-tao-kien-thuc/for-combobox`
};

export const diemQuyDoiRouter = {
  create: `/system/v1/diem-quy-doi`,
  update: `/system/v1/diem-quy-doi/`,
  delete: `/system/v1/diem-quy-doi/`,
  getById: `/system/v1/diem-quy-doi/`,
  getFilter: `/system/v1/diem-quy-doi/filter`,
  getCombobox: `/system/v1/diem-quy-doi/for-combobox`
};

export const thanhPhanMonTheoHeRouter = {
  create: `/system/v1/thanh-phan-mon-theo-he`,
  update: `/system/v1/thanh-phan-mon-theo-he/`,
  delete: `/system/v1/thanh-phan-mon-theo-he/`,
  getById: `/system/v1/thanh-phan-mon-theo-he/`,
  getFilter: `/system/v1/thanh-phan-mon-theo-he/filter`,
  getCombobox: `/system/v1/thanh-phan-mon-theo-he/for-combobox`
};

export const loaiThuChiRouter = {
  create: `/system/v1/loai-thu-chi`,
  update: `/system/v1/loai-thu-chi/`,
  delete: `/system/v1/loai-thu-chi/`,
  getById: `/system/v1/loai-thu-chi/`,
  getFilter: `/system/v1/loai-thu-chi/filter`,
  getCombobox: `/system/v1/loai-thu-chi/for-combobox`
};

export const khoaHocChiRouter = {
  getCombobox: `/system/v1/khoa-hoc/for-combobox`
};

export const phongHocRouter = {
  create: `/system/v1/phong-hoc`,
  update: `/system/v1/phong-hoc/`,
  delete: `/system/v1/phong-hoc/`,
  getById: `/system/v1/phong-hoc/`,
  getFilter: `/system/v1/phong-hoc/filter`,
  getCombobox: `/system/v1/phong-hoc/for-combobox`
};

export const capRenLuyenRouter = {
  create: `/system/v1/cap-ren-luyen`,
  update: `/system/v1/cap-ren-luyen/`,
  delete: `/system/v1/cap-ren-luyen/`,
  getById: `/system/v1/cap-ren-luyen/`,
  getFilter: `/system/v1/cap-ren-luyen/filter`,
  getCombobox: `/system/v1/cap-ren-luyen/for-combobox`
};

export const tangRouter = {
  create: `/system/v1/tang`,
  update: `/system/v1/tang/`,
  delete: `/system/v1/tang/`,
  getById: `/system/v1/tang/`,
  getFilter: `/system/v1/tang/filter`,
  getCombobox: `/system/v1/tang/for-combobox`
};

export const loaiPhongRouter = {
  create: `/system/v1/loai-phong`,
  update: `/system/v1/loai-phong/`,
  delete: `/system/v1/loai-phong/`,
  getById: `/system/v1/loai-phong/`,
  getFilter: `/system/v1/loai-phong/filter`,
  getCombobox: `/system/v1/loai-phong/for-combobox`
};

export const thamSoHeThongRouter = {
  create: `/system/v1/tham-so-he-thong`,
  getFilter: `/system/v1/tham-so-he-thong/filter`,
  getById: `/system/v1/tham-so-he-thong/`,
  update: `/system/v1/tham-so-he-thong/`,
  delete: `/system/v1/tham-so-he-thong/`,
  getCombobox: `/system/v1/tham-so-he-thong/for-combobox`,
  getByPhanHe: `/system/v1/tham-so-he-thong/by-phan-he`
};
