import { Component, ElementRef, Inject, OnInit, ViewChild } from '@angular/core';
import { I18NService } from '@core';
import { ACLService } from '@delon/acl';
import { ALAIN_I18N_TOKEN } from '@delon/theme';
import { ButtonModel, GridModel, QueryFilerModel } from '@model';
import { ThamSoHeThongApiService } from '@service';
import { BtnCellRenderComponent, StatusCellRenderComponent } from '@shared';
import {
  AG_GIRD_CELL_STYLE,
  EVENT_TYPE,
  EXCEL_STYLES_DEFAULT,
  FORM_TYPE,
  LIST_STATUS,
  OVERLAY_LOADING_TEMPLATE,
  OVERLAY_NOROW_TEMPLATE,
  PAGE_SIZE_OPTION_DEFAULT,
  QUERY_FILTER_DEFAULT
} from '@util';
import * as moment from 'moment';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzNotificationService } from 'ng-zorro-antd/notification';
import { Subscription } from 'rxjs';

import { ThamSoHeThongItemComponent } from '../tham-so-he-thong-item/tham-so-he-thong-item.component';

@Component({
  selector: 'app-tham-so-he-thong',
  templateUrl: './tham-so-he-thong.component.html',
  styleUrls: ['./tham-so-he-thong.component.less']
})
export class ThamSoHeThongComponent implements OnInit {
  constructor(
    @Inject(ALAIN_I18N_TOKEN) private i18n: I18NService,
    private notification: NzNotificationService,
    private modalService: NzModalService,
    private aclService: ACLService,
    private thamSoHeThongApiService: ThamSoHeThongApiService
  ) {
    this.btnAdd = {
      title: this.i18n.fanyi('function.tham-so-he-thong.button.add'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: () => this.onAddItem()
    };
    this.btnDelete = {
      title: this.i18n.fanyi('function.tham-so-he-thong.button.delete'),
      visible: false,
      enable: true,
      grandAccess: true,
      click: () => this.onDeleteItem()
    };
    this.btnResetSearch = {
      title: this.i18n.fanyi('app.common.button.reset-search'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: () => this.onResetSearch(true)
    };
    this.btnSearch = {
      title: this.i18n.fanyi('app.common.button.search'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: () => this.initGridData()
    };
    this.btnReload = {
      title: this.i18n.fanyi('app.common.button.reload'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: () => this.initGridData()
    };

    this.pageSizeOptions = PAGE_SIZE_OPTION_DEFAULT;
    this.excelStyles = EXCEL_STYLES_DEFAULT;

    this.columnDefs = [
      { field: 'index', headerName: this.i18n.fanyi('app.common.table.index'), minWidth: 80, flex: 1 },
      { field: 'phanHe', headerName: this.i18n.fanyi('function.tham-so-he-thong.table.phanHe'), minWidth: 150, flex: 1 },
      { field: 'nhomThamSo', headerName: this.i18n.fanyi('function.tham-so-he-thong.table.nhomThamSo'), minWidth: 180, flex: 1 },
      { field: 'idThamSo', headerName: this.i18n.fanyi('function.tham-so-he-thong.table.idThamSo'), minWidth: 150, flex: 1 },
      { field: 'tenThamSo', headerName: this.i18n.fanyi('function.tham-so-he-thong.table.tenThamSo'), minWidth: 200, flex: 1 },
      { field: 'giaTri', headerName: this.i18n.fanyi('function.tham-so-he-thong.table.giaTri'), minWidth: 180, flex: 1 },
      {
        field: 'active',
        headerName: this.i18n.fanyi('function.tham-so-he-thong.table.active'),
        minWidth: 120,
        flex: 1,
        cellRenderer: 'statusCellRender'
      },
      {
        headerName: this.i18n.fanyi('app.common.table.grid-action'),
        minWidth: 150,
        cellRenderer: 'btnCellRender',
        cellRendererParams: {
          infoClicked: (item: any) => this.onViewItem(item),
          editClicked: (item: any) => this.onEditItem(item),
          deleteClicked: (item: any) => this.onDeleteItem(item)
        }
      }
    ];
    this.defaultColDef = {
      minWidth: 100,
      cellStyle: AG_GIRD_CELL_STYLE,
      resizable: true
    };
    this.frameworkComponents = {
      btnCellRender: BtnCellRenderComponent,
      statusCellRender: StatusCellRenderComponent
    };
  }

  @ViewChild(ThamSoHeThongItemComponent, { static: false }) itemModal!: { initData: (arg0: {}, arg1: string, option: any) => void };

  isRenderComplete = false;

  listStatus = LIST_STATUS;
  filter: QueryFilerModel = { ...QUERY_FILTER_DEFAULT };
  pageSizeOptions: any[] = [];

  columnDefs: any[] = [];
  grid: GridModel = {
    dataCount: 0,
    rowData: [],
    totalData: 0
  };

  private gridApi: any;
  private gridColumnApi: any;
  defaultColDef: any;
  rowSelection = 'multiple';
  overlayLoadingTemplate = OVERLAY_LOADING_TEMPLATE;
  overlayNoRowsTemplate = OVERLAY_NOROW_TEMPLATE;
  quickText = '';
  excelStyles: any;
  frameworkComponents: any;

  btnAdd: ButtonModel;
  btnDelete: ButtonModel;
  btnResetSearch: ButtonModel;
  btnSearch: ButtonModel;
  btnReload: ButtonModel;

  isLoading = false;
  isShowDelete = false;

  title = this.i18n.fanyi('function.tham-so-he-thong.page.title');

  modal: any = {
    type: '',
    item: {},
    isShow: false,
    option: {}
  };

  // Filter by phanHe
  selectedPhanHe: string | undefined = undefined;
  listPhanHe: any[] = [];

  ngOnInit(): void {
    this.initRightOfUser();
    this.loadPhanHeList();
    this.isRenderComplete = true;
  }

  initRightOfUser(): void {
    this.btnAdd.grandAccess = this.aclService.canAbility('THAM_SO_HE_THONG_ADD');
    this.btnDelete.grandAccess = this.aclService.canAbility('THAM_SO_HE_THONG_DELETE');
  }

  loadPhanHeList(): void {
    // TODO: Load phanHe list from API if needed
    // For now, we'll populate it from the data when it loads
  }

  //#region Ag-grid
  onPageSizeChange(): void {
    this.initGridData();
  }

  onPageNumberChange(): void {
    this.initGridData();
  }

  onGridReady(params: any): void {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.initGridData();
  }

  onSelectionChanged($event: any): void {
    const selectedRows = this.gridApi.getSelectedRows();
    if (selectedRows.length > 0) {
      this.btnDelete.visible = true;
    } else {
      this.btnDelete.visible = false;
    }
  }

  onCellDoubleClicked($event: any): void {
    this.onViewItem($event.data);
  }
  //#endregion Ag-grid

  //#region Event
  onResetSearch(reloadData: boolean): void {
    this.filter.pageNumber = 1;
    this.filter.textSearch = undefined;
    this.selectedPhanHe = undefined;
    if (reloadData) {
      this.initGridData();
    }
  }

  onPhanHeChange(): void {
    this.filter.pageNumber = 1;
    this.initGridData();
  }

  onAddItem(): void {
    this.modal = {
      type: FORM_TYPE.ADD,
      item: {},
      isShow: true,
      option: {}
    };
    this.itemModal.initData({}, FORM_TYPE.ADD, {});
  }

  onEditItem(item: any = null): void {
    if (item === null) {
      const selectedRows = this.gridApi.getSelectedRows();
      item = selectedRows[0];
    }
    this.modal = {
      type: FORM_TYPE.EDIT,
      item,
      isShow: true,
      option: {}
    };
    this.itemModal.initData(item, FORM_TYPE.EDIT, {});
  }

  onViewItem(item: any = null): void {
    if (item === null) {
      const selectedRows = this.gridApi.getSelectedRows();
      item = selectedRows[0];
    }
    this.modal = {
      type: FORM_TYPE.INFO,
      item,
      isShow: true,
      option: {}
    };
    this.itemModal.initData(item, FORM_TYPE.INFO, {});
  }

  onDeleteItem(item: any = null): void {
    let title = `${this.i18n.fanyi('function.tham-so-he-thong.confirm-delete.title')}`;
    let content = `${this.i18n.fanyi('function.tham-so-he-thong.confirm-delete.content')} ${item.tenThamSo}`;
    this.modalService.confirm({
      nzTitle: title,
      nzContent: content,
      nzOkText: `${this.i18n.fanyi('app.confirm-delete.ok-text')}`,
      nzOkType: 'primary',
      nzOkDanger: true,
      nzOnOk: () => this.deleteItem(item.idPh, item.idThamSo),
      nzCancelText: `${this.i18n.fanyi('app.confirm-delete.cancel-text')}`,
      nzOnCancel: () => {}
    });
  }

  //#endregion Event

  //#region Modal
  onModalEventEmmit(event: any): void {
    this.modal.isShow = false;
    if (event.type === EVENT_TYPE.SUCCESS) {
      this.initGridData();
    }
  }
  //#endregion Modal

  //#region API Event
  deleteItem(idPh: any, idThamSo: any): Subscription {
    this.isLoading = true;
    const promise = this.thamSoHeThongApiService.delete(idPh, idThamSo).subscribe({
      next: (res: any) => {
        if (res.data === null || res.data === undefined) {
          this.notification.error(this.i18n.fanyi('app.common.error.title'), `${res.message}`);
          return;
        }
        this.initGridData();
      },
      error: (err: any) => {
        this.isLoading = false;
      },
      complete: () => (this.isLoading = false)
    });
    return promise;
  }

  initGridData(): Subscription | undefined {
    this.isLoading = true;
    this.btnDelete.visible = false;
    this.gridApi.showLoadingOverlay();

    // Add phanHe filter to the request
    const filterWithPhanHe = { ...this.filter };
    if (this.selectedPhanHe) {
      filterWithPhanHe['phanHe'] = this.selectedPhanHe;
    }

    const rs = this.thamSoHeThongApiService.getFilter(filterWithPhanHe).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (res.data === null || res.data === undefined) {
          this.notification.error(this.i18n.fanyi('app.common.error.title'), `${res.message}`);
          return;
        }

        const dataResult = res.data;

        let i = (this.filter.pageSize ?? 0) * ((this.filter.pageNumber ?? 0) - 1);

        for (const item of dataResult.data) {
          item.index = ++i;
          item.infoGrantAccess = true;
          item.editGrantAccess = this.aclService.canAbility('THAM_SO_HE_THONG_EDIT');
          item.deleteGrantAccess = this.aclService.canAbility('THAM_SO_HE_THONG_DELETE');
        }

        this.grid.rowData = dataResult.data;
        this.grid.totalData = dataResult.totalData;
        this.grid.dataCount = dataResult.dataCount;

        // Extract unique phanHe values for filter dropdown
        const phanHeSet = new Set(dataResult.data.map((item: any) => item.phanHe));
        this.listPhanHe = Array.from(phanHeSet).map(phanHe => ({ value: phanHe, label: phanHe }));

        this.gridApi.hideOverlay();
      },
      error: (err: any) => {
        this.isLoading = false;
        this.gridApi.hideOverlay();
      },
      complete: () => (this.isLoading = false)
    });
    return rs;
  }
  //#endregion API Event
}
