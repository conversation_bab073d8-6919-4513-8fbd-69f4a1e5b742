<PERSON><PERSON><PERSON> hình danh sách tham số hệ thống có các chức năng:

## Hướng dẫn cơ bản
    Lấy mẫu code từ chức năng "Mẫu email" tại thư mục "src\app\routes\system\email-template"
    Lấy các thông tin postman trong file "request\tham-so-he-thong\postman-tham-so-he-thong.txt"

## Danh sách quyền truy cập chức năng
        [Display(GroupName = "Tham số hệ thống", Name = "Xem thông tin Tham số hệ thống")]
        THAM_SO_HE_THONG_VIEW,

        [Display(GroupName = "Tham số hệ thống", Name = "Thêm thông tin Tham số hệ thống")]
        THAM_SO_HE_THONG_ADD,

        [Display(GroupName = "Tham số hệ thống", Name = "Sửa thông tin Tham số hệ thống")]
        THAM_SO_HE_THONG_EDIT,

        [Display(GroupName = "Tham số hệ thống", Name = "<PERSON><PERSON>a thông tin Tham số hệ thống")]
        THAM_SO_HE_THONG_DELETE,

1. Xem danh sách
    Hiển thị danh sách tham số hệ thống, có thêm bộ lọc theo phân hệ
    Hiển thị các thông tin sau
        "phanHe": "string"
        "nhomThamSo": "string",
        "idThamSo": "string",
        "tenThamSo": "string",
        "giaTri": "string",
        "active": true,

2. Thêm mới, sửa, xóa
    Gom chung vào 1 màn hình như các chức năng khác để cùng thao tác thêm, sửa, xóa







