<nz-modal
  [(nzVisible)]="isVisible"
  [nzTitle]="modalTitle"
  [nzContent]="modalContent"
  [nzFooter]="modalFooter"
  nzMaskClosable="false"
  nzWidth="1000px"
  (nzOnCancel)="handleCancel()"
>
  <ng-template #modalTitle> {{ 'menu.workflow-function' | i18n }} </ng-template>

  <ng-template #modalContent>
    <form nz-form [formGroup]="form" (ngSubmit)="save()">
      <!-- <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzFor="phanHe">{{ 'workflow-map-function.table.phan-he' | i18n }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24">
          <input nz-input formControlName="phanHe" id="phanHe" />
        </nz-form-control>
      </nz-form-item> -->
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzFor="tenThamSo">{{ 'workflow-map-function.table.ten-chuc-nang' | i18n }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24">
          <input nz-input formControlName="tenThamSo" id="tenThamSo" />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="giaTri">{{ 'workflow-map-function.table.gia-tri' | i18n }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="{{ 'function.benh-vien.modal.form.maBenhVien.required' | i18n }}">
          <nz-select
            nzShowSearch
            id="giaTri"
            formControlName="giaTri"
            class="custom-select"
            name="giaTri"
            nzPlaceHolder="{{ 'workflow-map-function.table.phan-he' | i18n }}"
          >
            <nz-option *ngFor="let p of lstQuyTrinh" [nzValue]="p.workflowName" [nzLabel]="p.workflowName"></nz-option>
          </nz-select>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzFor="active">{{ 'workflow-map-function-item.table.active' | i18n }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24">
          <label nz-checkbox formControlName="active" id="active"></label>
        </nz-form-control>
      </nz-form-item>
    </form>
  </ng-template>

  <ng-template #modalFooter>
    <button nz-button nzType="primary" class="btn-primary" [nzLoading]="isLoading" (click)="btnSave.click($event)">
      <i nz-icon nzType="save" nzTheme="fill"></i>{{ btnSave.title }}
    </button>
    <button nz-button nzType="default" class="btn-warning" (click)="btnCancel.click($event)">
      <i nz-icon nzType="close-circle" nzTheme="fill"></i>{{ btnCancel.title }}
    </button>
  </ng-template>
</nz-modal>
