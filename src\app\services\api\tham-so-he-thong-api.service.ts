import { Injectable } from '@angular/core';
import { _HttpClient } from '@delon/theme';
import { environment } from '@env/environment';
import { QueryFilerModel } from '@model';
import { thamSoHeThongRouter } from '@util';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ThamSoHeThongApiService {
  constructor(private http: _HttpClient) {}

  create(model: any): Observable<any> {
    return this.http.post(environment.api.baseUrl + thamSoHeThongRouter.create, model);
  }

  getFilter(model: QueryFilerModel): Observable<any> {
    return this.http.post(environment.api.baseUrl + thamSoHeThongRouter.getFilter, model);
  }

  getById(idPh: any, idThamSo: any): Observable<any> {
    return this.http.get(`${environment.api.baseUrl}${thamSoHeThongRouter.getById}${idPh}/${idThamSo}`);
  }

  update(idThamSo: any, model: any): Observable<any> {
    return this.http.put(environment.api.baseUrl + thamSoHeThongRouter.update + idThamSo, model);
  }

  delete(idPh: any, idThamSo: any): Observable<any> {
    return this.http.delete(`${environment.api.baseUrl}${thamSoHeThongRouter.delete}${idPh}/${idThamSo}`);
  }

  getCombobox(count?: number, ts?: string, idPh?: number, nhomThamSo?: string): Observable<any> {
    let params = '';
    const queryParams = [];
    if (count !== undefined) queryParams.push(`count=${count}`);
    if (ts !== undefined) queryParams.push(`ts=${ts}`);
    if (idPh !== undefined) queryParams.push(`idPh=${idPh}`);
    if (nhomThamSo !== undefined) queryParams.push(`nhomThamSo=${nhomThamSo}`);

    if (queryParams.length > 0) {
      params = `?${queryParams.join('&')}`;
    }

    return this.http.get(environment.api.baseUrl + thamSoHeThongRouter.getCombobox + params);
  }

  getByPhanHe(phanHe: string): Observable<any> {
    return this.http.get(`${environment.api.baseUrl}${thamSoHeThongRouter.getByPhanHe}?phanHe=${phanHe}`);
  }
}
