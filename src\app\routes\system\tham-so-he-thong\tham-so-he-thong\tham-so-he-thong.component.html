<nz-card [nzBordered]="false" [nzTitle]="title">
  <div nz-row [nz<PERSON><PERSON>]="24" class="pt-lg">
    <nz-row>
      <nz-col nzSm="4" nzXS="24" nzMd="4" class="padding-bottom-10">
        <button
          nz-button
          nzType="primary"
          [disabled]="!btnAdd.grandAccess"
          [nzLoading]="btnAdd.isLoading"
          [style.display]="btnAdd.visible ? 'inline-block' : 'none'"
          (click)="btnAdd.click($event)"
        >
          <i nz-icon nzType="plus" nzTheme="outline"></i>{{ btnAdd.title }}
        </button>
      </nz-col>
      <nz-col nzSm="4" nzXS="24" nzMd="4" class="padding-bottom-10">
        <button
          nz-button
          nzType="primary"
          nzDanger
          [disabled]="!btnDelete.grandAccess"
          [nzLoading]="btnDelete.isLoading"
          [style.display]="btnDelete.visible ? 'inline-block' : 'none'"
          (click)="btnDelete.click($event)"
        >
          <i nz-icon nzType="delete" nzTheme="fill"></i>{{ btnDelete.title }}
        </button>
      </nz-col>
      <nz-col nzSm="4" nzXS="24" nzMd="4" class="padding-bottom-10">
        <nz-select
          nzPlaceHolder="{{ 'function.tham-so-he-thong.filter.phanHe.placeholder' | i18n }}"
          [(ngModel)]="selectedPhanHe"
          (ngModelChange)="onPhanHeChange()"
          nzAllowClear
          style="width: 100%"
        >
          <nz-option *ngFor="let item of listPhanHe" [nzValue]="item.value" [nzLabel]="item.label"></nz-option>
        </nz-select>
      </nz-col>
      <nz-col nzSm="8" nzXS="24" nzMd="8" class="pull-right padding-bottom-10">
        <nz-input-group nzSearch [nzAddOnAfter]="suffixIconButton">
          <input
            type="text"
            [(ngModel)]="filter.textSearch"
            nz-input
            placeholder="{{ 'function.tham-so-he-thong.search-box.placeholder' | i18n }}"
            (keyup.enter)="initGridData()"
          />
        </nz-input-group>
        <ng-template #suffixIconButton>
          <button nz-button nzType="default" nzSearch (click)="initGridData()"><span nz-icon nzType="search"></span></button>
        </ng-template>
      </nz-col>
    </nz-row>
    <nz-row>
      <ag-grid-angular
        #agGrid
        style="width: 100%; height: 70vh"
        id="tham-so-he-thong-grid"
        class="ag-theme-alpine"
        [columnDefs]="columnDefs"
        [defaultColDef]="defaultColDef"
        [rowData]="grid.rowData"
        [overlayLoadingTemplate]="overlayLoadingTemplate"
        [overlayNoRowsTemplate]="overlayNoRowsTemplate"
        [frameworkComponents]="frameworkComponents"
        [excelStyles]="excelStyles"
        (gridReady)="onGridReady($event)"
        (selectionChanged)="onSelectionChanged($event)"
        (cellDoubleClicked)="onCellDoubleClicked($event)"
      >
      </ag-grid-angular>
    </nz-row>
    <nz-row class="mt-sm">
      <nz-col nzSm="12" nzXs="24">
        <nz-pagination
          [(nzPageIndex)]="filter.pageNumber"
          [(nzPageSize)]="filter.pageSize"
          [nzTotal]="grid.totalData"
          [nzPageSizeOptions]="pageSizeOptions"
          nzShowSizeChanger
          [nzShowTotal]="totalTemplate"
          (nzPageIndexChange)="onPageNumberChange()"
          (nzPageSizeChange)="onPageSizeChange()"
        >
        </nz-pagination>
        <ng-template #totalTemplate let-total let-range="range">
          {{ 'app.common.pagination.total' | i18n : { total: total, range0: range[0], range1: range[1] } }}
        </ng-template>
      </nz-col>
    </nz-row>
  </div>
</nz-card>

<app-tham-so-he-thong-item
  #itemModal
  [isVisible]="modal.isShow"
  [item]="modal.item"
  [type]="modal.type"
  [option]="modal.option"
  (eventEmmit)="onModalEventEmmit($event)"
>
</app-tham-so-he-thong-item>
