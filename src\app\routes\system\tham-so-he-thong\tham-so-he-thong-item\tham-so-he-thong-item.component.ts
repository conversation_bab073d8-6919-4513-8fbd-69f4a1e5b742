import { Component, EventEmitter, Inject, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { I18NService } from '@core';
import { ACLService } from '@delon/acl';
import { ALAIN_I18N_TOKEN } from '@delon/theme';
import { ButtonModel } from '@model';
import { ThamSoHeThongApiService } from '@service';
import { EVENT_TYPE, FORM_TYPE } from '@util';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-tham-so-he-thong-item',
  templateUrl: './tham-so-he-thong-item.component.html',
  styleUrls: ['./tham-so-he-thong-item.component.less']
})
export class ThamSoHeThongItemComponent implements OnInit {
  @Input() type = 'add';
  @Input() item: any;
  @Input() isVisible = false;
  @Input() option: any;
  @Output() readonly eventEmmit = new EventEmitter<any>();

  form: FormGroup;

  isInfo = false;
  isEdit = false;
  isAdd = false;
  tittle = '';

  isLoading = false;

  btnSave: ButtonModel;
  btnCancel: ButtonModel;
  btnEdit: ButtonModel;

  constructor(
    @Inject(ALAIN_I18N_TOKEN) private i18n: I18NService,
    private fb: FormBuilder,
    private notification: NzMessageService,
    private aclService: ACLService,
    private thamSoHeThongApiService: ThamSoHeThongApiService
  ) {
    this.btnSave = {
      title: this.i18n.fanyi('app.common.button.save'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: () => this.save()
    };
    this.btnCancel = {
      title: this.i18n.fanyi('app.common.button.cancel'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: () => this.handleCancel()
    };
    this.btnEdit = {
      title: this.i18n.fanyi('app.common.button.edit'),
      visible: true,
      enable: true,
      grandAccess: true,
      click: () => this.onEdit()
    };

    this.form = this.fb.group({
      phanHe: [null, [Validators.required]],
      nhomThamSo: [null, [Validators.required]],
      idThamSo: [null, [Validators.required]],
      tenThamSo: [null, [Validators.required]],
      giaTri: [null, [Validators.required]],
      active: [true]
    });
  }

  handleCancel(): void {
    this.isVisible = false;
    this.eventEmmit.emit({ type: EVENT_TYPE.CLOSE });
  }

  ngOnInit(): void {
    this.initRightOfUser();
  }

  initRightOfUser(): void {
    this.btnSave.grandAccess = this.aclService.canAbility('THAM_SO_HE_THONG_ADD') || this.aclService.canAbility('THAM_SO_HE_THONG_EDIT');
    this.btnEdit.grandAccess = this.aclService.canAbility('THAM_SO_HE_THONG_EDIT');
  }

  initData(item: any, type: string, option: any): void {
    this.item = item;
    this.type = type;
    this.option = option;

    this.isInfo = type === FORM_TYPE.INFO;
    this.isEdit = type === FORM_TYPE.EDIT;
    this.isAdd = type === FORM_TYPE.ADD;

    if (this.isAdd) {
      this.tittle = this.i18n.fanyi('function.tham-so-he-thong.modal.title.add');
      this.form.reset();
      this.form.patchValue({
        active: true
      });
    } else if (this.isEdit) {
      this.tittle = this.i18n.fanyi('function.tham-so-he-thong.modal.title.edit');
      this.form.patchValue({
        phanHe: item.phanHe,
        nhomThamSo: item.nhomThamSo,
        idThamSo: item.idThamSo,
        tenThamSo: item.tenThamSo,
        giaTri: item.giaTri,
        active: item.active
      });
    } else if (this.isInfo) {
      this.tittle = this.i18n.fanyi('function.tham-so-he-thong.modal.title.info');
      this.form.patchValue({
        phanHe: item.phanHe,
        nhomThamSo: item.nhomThamSo,
        idThamSo: item.idThamSo,
        tenThamSo: item.tenThamSo,
        giaTri: item.giaTri,
        active: item.active
      });
    }

    if (this.isInfo) {
      this.form.disable();
    } else {
      this.form.enable();
      if (this.isEdit) {
        // Disable key fields in edit mode
        this.form.get('phanHe')?.disable();
        this.form.get('idThamSo')?.disable();
      }
    }
  }

  onEdit(): void {
    this.isInfo = false;
    this.isEdit = true;
    this.tittle = this.i18n.fanyi('function.tham-so-he-thong.modal.title.edit');
    this.form.enable();
    // Disable key fields in edit mode
    this.form.get('phanHe')?.disable();
    this.form.get('idThamSo')?.disable();
  }

  save(): void {
    if (this.form.invalid) {
      Object.values(this.form.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty();
          control.updateValueAndValidity({ onlySelf: true });
        }
      });
      return;
    }

    const formValue = this.form.getRawValue();

    if (this.isAdd) {
      this.createItem(formValue);
    } else if (this.isEdit) {
      this.updateItem(formValue);
    }
  }

  createItem(model: any): Subscription {
    this.isLoading = true;
    const promise = this.thamSoHeThongApiService.create(model).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (res.data === null || res.data === undefined) {
          // this.notification.error(this.i18n.fanyi('app.common.error.title'), `${res.message}`);
          return;
        }
        this.notification.success(
          this.i18n.fanyi('app.common.success.title')
          // this.i18n.fanyi('function.tham-so-he-thong.message.create-success')
        );
        this.eventEmmit.emit({ type: EVENT_TYPE.SUCCESS });
      },
      error: (err: any) => {
        this.isLoading = false;
        this.notification.error(this.i18n.fanyi('app.common.error.title'), err.message || 'An error occurred');
      },
      complete: () => (this.isLoading = false)
    });
    return promise;
  }

  updateItem(model: any): Subscription {
    this.isLoading = true;
    const promise = this.thamSoHeThongApiService.update(this.item.idThamSo, model).subscribe({
      next: (res: any) => {
        this.isLoading = false;
        if (res.data === null || res.data === undefined) {
          // this.notification.error(this.i18n.fanyi('app.common.error.title'), `${res.message}`);
          return;
        }
        this.notification.success(
          // this.i18n.fanyi('app.common.success.title'),
          this.i18n.fanyi('function.tham-so-he-thong.message.update-success')
        );
        this.eventEmmit.emit({ type: EVENT_TYPE.SUCCESS });
      },
      error: (err: any) => {
        this.isLoading = false;
        this.notification.error(this.i18n.fanyi('app.common.error.title'), err.message || 'An error occurred');
      },
      complete: () => (this.isLoading = false)
    });
    return promise;
  }
}
