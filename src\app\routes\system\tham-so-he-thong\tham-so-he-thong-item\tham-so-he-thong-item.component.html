<nz-modal
  [(nzVisible)]="isVisible"
  [nzTitle]="tittle"
  [nzContent]="modalContent"
  [nzFooter]="modalFooter"
  nzWidth="800px"
  [nzClosable]="false"
  [nzMaskClosable]="false"
  (nzOnCancel)="handleCancel()"
>
  <ng-template #modalContent>
    <form nz-form [formGroup]="form" (ngSubmit)="save()">
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="phanHe">{{
          'function.tham-so-he-thong.modal.form.phanHe' | i18n
        }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="{{ 'function.tham-so-he-thong.modal.form.phanHe.required' | i18n }}">
          <input
            nz-input
            formControlName="phanHe"
            id="phanHe"
            placeholder="{{ 'function.tham-so-he-thong.modal.form.phanHe.place-holder' | i18n }}"
          />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="nhomThamSo">{{
          'function.tham-so-he-thong.modal.form.nhomThamSo' | i18n
        }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="{{ 'function.tham-so-he-thong.modal.form.nhomThamSo.required' | i18n }}">
          <input
            nz-input
            formControlName="nhomThamSo"
            id="nhomThamSo"
            placeholder="{{ 'function.tham-so-he-thong.modal.form.nhomThamSo.place-holder' | i18n }}"
          />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="idThamSo">{{
          'function.tham-so-he-thong.modal.form.idThamSo' | i18n
        }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="{{ 'function.tham-so-he-thong.modal.form.idThamSo.required' | i18n }}">
          <input
            nz-input
            formControlName="idThamSo"
            id="idThamSo"
            placeholder="{{ 'function.tham-so-he-thong.modal.form.idThamSo.place-holder' | i18n }}"
          />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="tenThamSo">{{
          'function.tham-so-he-thong.modal.form.tenThamSo' | i18n
        }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="{{ 'function.tham-so-he-thong.modal.form.tenThamSo.required' | i18n }}">
          <input
            nz-input
            formControlName="tenThamSo"
            id="tenThamSo"
            placeholder="{{ 'function.tham-so-he-thong.modal.form.tenThamSo.place-holder' | i18n }}"
          />
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzRequired nzFor="giaTri">{{
          'function.tham-so-he-thong.modal.form.giaTri' | i18n
        }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24" nzErrorTip="{{ 'function.tham-so-he-thong.modal.form.giaTri.required' | i18n }}">
          <nz-textarea-count [nzMaxCharacterCount]="1000">
            <textarea
              nz-input
              formControlName="giaTri"
              id="giaTri"
              placeholder="{{ 'function.tham-so-he-thong.modal.form.giaTri.place-holder' | i18n }}"
              [nzAutosize]="{ minRows: 3, maxRows: 6 }"
            ></textarea>
          </nz-textarea-count>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-label [nzSm]="6" [nzXs]="24" nzFor="active">{{ 'function.tham-so-he-thong.modal.form.active' | i18n }}</nz-form-label>
        <nz-form-control [nzSm]="17" [nzXs]="24">
          <nz-switch formControlName="active" id="active"></nz-switch>
        </nz-form-control>
      </nz-form-item>
    </form>
  </ng-template>
  <ng-template #modalFooter>
    <button
      nz-button
      nzType="default"
      [disabled]="!btnCancel.grandAccess"
      [nzLoading]="btnCancel.isLoading"
      [style.display]="btnCancel.visible ? 'inline-block' : 'none'"
      (click)="btnCancel.click($event)"
    >
      {{ btnCancel.title }}
    </button>
    <button
      *ngIf="isInfo"
      nz-button
      nzType="primary"
      [disabled]="!btnEdit.grandAccess"
      [nzLoading]="btnEdit.isLoading"
      [style.display]="btnEdit.visible ? 'inline-block' : 'none'"
      (click)="btnEdit.click($event)"
    >
      {{ btnEdit.title }}
    </button>
    <button
      *ngIf="!isInfo"
      nz-button
      nzType="primary"
      [disabled]="!btnSave.grandAccess"
      [nzLoading]="btnSave.isLoading"
      [style.display]="btnSave.visible ? 'inline-block' : 'none'"
      (click)="btnSave.click($event)"
    >
      {{ btnSave.title }}
    </button>
  </ng-template>
</nz-modal>
