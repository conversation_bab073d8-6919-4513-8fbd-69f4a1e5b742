import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CKEditorModule } from '@ckeditor/ckeditor5-angular';
import { WorkflowDesignerModule } from '@optimajet/workflow-designer-angular';
import { SharedModule } from '@shared';
import { AgGridModule } from 'ag-grid-angular';

import { BenhVienItemComponent } from './catalog/benh-vien/benh-vien-item/benh-vien-item.component';
import { BenhVienComponent } from './catalog/benh-vien/benh-vien/benh-vien.component';
import { BoMonItemComponent } from './catalog/bo-mon/bo-mon-item/bo-mon-item.component';
import { BoMonComponent } from './catalog/bo-mon/bo-mon/bo-mon.component';
import { CapKhenThuongKyLuatItemComponent } from './catalog/cap-khen-thuong-ky-luat/cap-khen-thuong-ky-luat-item/cap-khen-thuong-ky-luat-item.component';
import { CapKhenThuongKyLuatComponent } from './catalog/cap-khen-thuong-ky-luat/cap-khen-thuong-ky-luat/cap-khen-thuong-ky-luat.component';
import { CapRenLuyenItemComponent } from './catalog/cap-ren-luyen/cap-ren-luyen-item/cap-ren-luyen-item.component';
import { CapRenLuyenComponent } from './catalog/cap-ren-luyen/cap-ren-luyen/cap-ren-luyen.component';
import { ChiTieuTuyenSinhItemComponent } from './catalog/chi-tieu-tuyen-sinh/chi-tieu-tuyen-sinh-item/chi-tieu-tuyen-sinh-item.component';
import { ChiTieuTuyenSinhComponent } from './catalog/chi-tieu-tuyen-sinh/chi-tieu-tuyen-sinh/chi-tieu-tuyen-sinh.component';
import { ChucDanhItemComponent } from './catalog/chuc-danh/chuc-danh-item/chuc-danh-item.component';
import { ChucDanhComponent } from './catalog/chuc-danh/chuc-danh/chuc-danh.component';
import { ChuyenNganhItemComponent } from './catalog/chuyen-nganh/chuyen-nganh-item/chuyen-nganh-item.component';
import { ChuyenNganhComponent } from './catalog/chuyen-nganh/chuyen-nganh/chuyen-nganh.component';
import { CoSoDaoTaoItemComponent } from './catalog/co-so-dao-tao/co-so-dao-tao-item/co-so-dao-tao-item.component';
import { CoSoDaoTaoComponent } from './catalog/co-so-dao-tao/co-so-dao-tao/co-so-dao-tao.component';
import { DanTocItemComponent } from './catalog/dan-toc/dan-toc-item/dan-toc-item.component';
import { DanTocComponent } from './catalog/dan-toc/dan-toc/dan-toc.component';
import { DiemQuyDoiItemComponent } from './catalog/diem-quy-doi/diem-quy-doi-item/diem-quy-doi-item.component';
import { DiemQuyDoiComponent } from './catalog/diem-quy-doi/diem-quy-doi/diem-quy-doi.component';
import { DiemRenLuyenQuyDoiItemComponent } from './catalog/diem-ren-luyen-quy-doi/diem-ren-luyen-quy-doi-item/diem-ren-luyen-quy-doi-item.component';
import { DiemRenLuyenQuyDoiComponent } from './catalog/diem-ren-luyen-quy-doi/diem-ren-luyen-quy-doi/diem-ren-luyen-quy-doi.component';
import { DoiTuongHocBongItemComponent } from './catalog/doi-tuong-hoc-bong/doi-tuong-hoc-bong-item/doi-tuong-hoc-bong-item.component';
import { DoiTuongHocBongComponent } from './catalog/doi-tuong-hoc-bong/doi-tuong-hoc-bong/doi-tuong-hoc-bong.component';
import { DoiTuongHocPhiItemComponent } from './catalog/doi-tuong-nop-hoc-phi/doi-tuong-nop-hoc-phi-item/doi-tuong-hoc-phi-item.component';
import { DoiTuongHocPhiComponent } from './catalog/doi-tuong-nop-hoc-phi/doi-tuong-nop-hoc-phi/doi-tuong-hoc-phi.component';
import { DoiTuongItemComponent } from './catalog/doi-tuong/doi-tuong-item/doi-tuong-item.component';
import { DoiTuongComponent } from './catalog/doi-tuong/doi-tuong/doi-tuong.component';
import { DonViThucTapItemComponent } from './catalog/don-vi-thuc-tap/don-vi-thuc-tap-item/don-vi-thuc-tap-item.component';
import { DonViThucTapComponent } from './catalog/don-vi-thuc-tap/don-vi-thuc-tap/don-vi-thuc-tap.component';
import { GioiTinhItemComponent } from './catalog/gioi-tinh/gioi-tinh-item/gioi-tinh-item.component';
import { GioiTinhComponent } from './catalog/gioi-tinh/gioi-tinh/gioi-tinh.component';
import { HanhViItemComponent } from './catalog/hanh-vi/hanh-vi-item/hanh-vi-item.component';
import { HanhViComponent } from './catalog/hanh-vi/hanh-vi/hanh-vi.component';
import { HeDaoTaoItemComponent } from './catalog/he-dao-tao/he-dao-tao-item/he-dao-tao-item.component';
import { HeDaoTaoComponent } from './catalog/he-dao-tao/he-dao-tao/he-dao-tao.component';
import { HinhThucHocItemComponent } from './catalog/hinh-thuc-hoc/hinh-thuc-hoc-item/hinh-thuc-hoc-item.component';
import { HinhThucHocComponent } from './catalog/hinh-thuc-hoc/hinh-thuc-hoc/hinh-thuc-hoc.component';
import { HinhThucThiItemComponent } from './catalog/hinh-thuc-thi/hinh-thuc-thi-item/hinh-thuc-thi-item.component';
import { HinhThucThiComponent } from './catalog/hinh-thuc-thi/hinh-thuc-thi/hinh-thuc-thi.component';
import { HocHamItemComponent } from './catalog/hoc-ham/hoc-ham-item/hoc-ham-item.component';
import { HocHamComponent } from './catalog/hoc-ham/hoc-ham/hoc-ham.component';
import { HocKyDangKyItemComponent } from './catalog/hoc-ky-dang-ky/hoc-ky-dang-ky-item/hoc-ky-dang-ky-item.component';
import { HocKyDangKyComponent } from './catalog/hoc-ky-dang-ky/hoc-ky-dang-ky/hoc-ky-dang-ky.component';
import { HocViItemComponent } from './catalog/hoc-vi/hoc-vi-item/hoc-vi-item.component';
import { HocViComponent } from './catalog/hoc-vi/hoc-vi/hoc-vi.component';
import { HuyenItemComponent } from './catalog/huyen/huyen-item/huyen-item.component';
import { HuyenComponent } from './catalog/huyen/huyen/huyen.component';
import { KhoaItemComponent } from './catalog/khoa/khoa-item/khoa-item.component';
import { KhoaComponent } from './catalog/khoa/khoa/khoa.component';
import { KhoiKienThucItemComponent } from './catalog/khoi-kien-thuc/khoi-kien-thuc-item/khoi-kien-thuc-item.component';
import { KhoiKienThucComponent } from './catalog/khoi-kien-thuc/khoi-kien-thuc/khoi-kien-thuc.component';
import { KhuVucItemComponent } from './catalog/khu-vuc/khu-vuc-item/khu-vuc-item.component';
import { KhuVucComponent } from './catalog/khu-vuc/khu-vuc/khu-vuc.component';
import { LoaiChungChiItemComponent } from './catalog/loai-chung-chi/loai-chung-chi-item/loai-chung-chi-item.component';
import { LoaiChungChiComponent } from './catalog/loai-chung-chi/loai-chung-chi/loai-chung-chi.component';
import { LoaiGiayToItemComponent } from './catalog/loai-giay-to/loai-giay-to-item/loai-giay-to-item.component';
import { LoaiGiayToComponent } from './catalog/loai-giay-to/loai-giay-to/loai-giay-to.component';
import { LoaiKhenThuongItemComponent } from './catalog/loai-khen-thuong/loai-khen-thuong-item/loai-khen-thuong-item.component';
import { LoaiKhenThuongComponent } from './catalog/loai-khen-thuong/loai-khen-thuong/loai-khen-thuong.component';
import { LoaiPhongItemComponent } from './catalog/loai-phong/loai-phong-item/loai-phong-item.component';
import { LoaiPhongComponent } from './catalog/loai-phong/loai-phong/loai-phong.component';
import { LoaiQuyetDinhItemComponent } from './catalog/loai-quyet-dinh/loai-quyet-dinh-item/loai-quyet-dinh-item.component';
import { LoaiQuyetDinhComponent } from './catalog/loai-quyet-dinh/loai-quyet-dinh/loai-quyet-dinh.component';
import { LoaiRenLuyenItemComponent } from './catalog/loai-ren-luyen/loai-ren-luyen-item/loai-ren-luyen-item.component';
import { LoaiRenLuyenComponent } from './catalog/loai-ren-luyen/loai-ren-luyen/loai-ren-luyen.component';
import { LoaiThanhPhanDiemItemComponent } from './catalog/loai-thanh-phan-diem/loai-thanh-phan-diem-item/loai-thanh-phan-diem-item.component';
import { LoaiThanhPhanDiemComponent } from './catalog/loai-thanh-phan-diem/loai-thanh-phan-diem/loai-thanh-phan-diem.component';
import { LoaiThuChiItemComponent } from './catalog/loai-thu-chi/loai-thu-chi-item/loai-thu-chi-item.component';
import { LoaiThuChiComponent } from './catalog/loai-thu-chi/loai-thu-chi/loai-thu-chi.component';
import { MucHuongBhytItemComponent } from './catalog/muc-huong-bhyt/muc-huong-bhyt-item/muc-huong-bhyt-item.component';
import { MucHuongBhytComponent } from './catalog/muc-huong-bhyt/muc-huong-bhyt/muc-huong-bhyt.component';
import { NganhItemComponent } from './catalog/nganh/nganh-item/nganh-item.component';
import { NganhComponent } from './catalog/nganh/nganh/nganh.component';
import { NhomChungChiItemComponent } from './catalog/nhom-chung-chi/nhom-chung-chi-item/nhom-chung-chi-item.component';
import { NhomChungChiComponent } from './catalog/nhom-chung-chi/nhom-chung-chi/nhom-chung-chi.component';
import { NhomDoiTuongItemComponent } from './catalog/nhom-doi-tuong/nhom-doi-tuong-item/nhom-doi-tuong-item.component';
import { NhomDoiTuongComponent } from './catalog/nhom-doi-tuong/nhom-doi-tuong/nhom-doi-tuong.component';
import { PhongBanItemComponent } from './catalog/phong-ban/phong-ban-item/phong-ban-item.component';
import { PhongBanComponent } from './catalog/phong-ban/phong-ban/phong-ban.component';
import { PhongHocItemComponent } from './catalog/phong-hoc/phong-hoc-item/phong-hoc-item.component';
import { PhongHocComponent } from './catalog/phong-hoc/phong-hoc/phong-hoc.component';
import { PhuongAnItemComponent } from './catalog/phuong-an/phuong-an-item/phuong-an-item.component';
import { PhuongAnComponent } from './catalog/phuong-an/phuong-an/phuong-an.component';
import { PhuongNgoaiTruItemComponent } from './catalog/phuong-ngoai-tru/phuong-ngoai-tru-item/phuong-ngoai-tru-item.component';
import { PhuongNgoaiTruComponent } from './catalog/phuong-ngoai-tru/phuong-ngoai-tru/phuong-ngoai-tru.component';
import { PhuongThucDongItemComponent } from './catalog/phuong-thuc-dong/phuong-thuc-dong-item/phuong-thuc-dong-item.component';
import { PhuongThucDongComponent } from './catalog/phuong-thuc-dong/phuong-thuc-dong/phuong-thuc-dong.component';
import { QuocTichItemComponent } from './catalog/quoc-tich/quoc-tich-item/quoc-tich-item.component';
import { QuocTichComponent } from './catalog/quoc-tich/quoc-tich/quoc-tich.component';
import { TangItemComponent } from './catalog/tang/tang-item/tang-item.component';
import { TangComponent } from './catalog/tang/tang/tang.component';
import { ThanhPhanMonTheoHeItemComponent } from './catalog/thanh-phan-mon-theo-he/thanh-phan-mon-theo-he-item/thanh-phan-mon-theo-he-item.component';
import { ThanhPhanMonTheoHeComponent } from './catalog/thanh-phan-mon-theo-he/thanh-phan-mon-theo-he/thanh-phan-mon-theo-he.component';
import { TinhItemComponent } from './catalog/tinh/tinh-item/tinh-item.component';
import { TinhComponent } from './catalog/tinh/tinh/tinh.component';
import { ToaNhaItemComponent } from './catalog/toa-nha/toa-nha-item/toa-nha-item.component';
import { ToaNhaComponent } from './catalog/toa-nha/toa-nha/toa-nha.component';
import { TonGiaoItemComponent } from './catalog/ton-giao/ton-giao-item/ton-giao-item.component';
import { TonGiaoComponent } from './catalog/ton-giao/ton-giao/ton-giao.component';
import { VungItemComponent } from './catalog/vung/vung-item/vung-item.component';
import { VungComponent } from './catalog/vung/vung/vung.component';
import { XaItemComponent } from './catalog/xa/xa-item/xa-item.component';
import { XaComponent } from './catalog/xa/xa/xa.component';
import { XepHangHocLucItemComponent } from './catalog/xep-hang-hoc-luc/xep-hang-hoc-luc-item/xep-hang-hoc-luc-item.component';
import { XepHangHocLucComponent } from './catalog/xep-hang-hoc-luc/xep-hang-hoc-luc/xep-hang-hoc-luc.component';
import { XepHangNamDaoTaoItemComponent } from './catalog/xep-hang-nam-dao-tao/xep-hang-nam-dao-tao-item/xep-hang-nam-dao-tao-item.component';
import { XepHangNamDaoTaoComponent } from './catalog/xep-hang-nam-dao-tao/xep-hang-nam-dao-tao/xep-hang-nam-dao-tao.component';
import { XepLoaiChungChiItemComponent } from './catalog/xep-loai-chung-chi/xep-loai-chung-chi-item/xep-loai-chung-chi-item.component';
import { XepLoaiChungChiComponent } from './catalog/xep-loai-chung-chi/xep-loai-chung-chi/xep-loai-chung-chi.component';
import { XepLoaiHocBongItemComponent } from './catalog/xep-loai-hoc-bong/xep-loai-hoc-bong-item/xep-loai-hoc-bong-item.component';
import { XepLoaiHocBongComponent } from './catalog/xep-loai-hoc-bong/xep-loai-hoc-bong/xep-loai-hoc-bong.component';
import { XepLoaiHocLucThang4ItemComponent } from './catalog/xep-loai-hoc-luc-thang-4/xep-loai-hoc-luc-thang-4-item/xep-loai-hoc-luc-thang-4-item.component';
import { XepLoaiHocLucThang4Component } from './catalog/xep-loai-hoc-luc-thang-4/xep-loai-hoc-luc-thang-4/xep-loai-hoc-luc-thang-4.component';
import { XepLoaiHocTapThangDiem10ItemComponent } from './catalog/xep-loai-hoc-tap-thang-10/xep-loai-hoc-tap-thang-10-item/xep-loai-hoc-tap-thang-10-item.component';
import { XepLoaiHocTapThangDiem10Component } from './catalog/xep-loai-hoc-tap-thang-10/xep-loai-hoc-tap-thang-10/xep-loai-hoc-tap-thang-10.component';
import { XepLoaiRenLuyenItemComponent } from './catalog/xep-loai-ren-luyen/xep-loai-ren-luyen-item/xep-loai-ren-luyen-item.component';
import { XepLoaiRenLuyenComponent } from './catalog/xep-loai-ren-luyen/xep-loai-ren-luyen/xep-loai-ren-luyen.component';
import { XepLoaiTotNghiepThang10ItemComponent } from './catalog/xep-loai-tot-nghiep-thang-10/xep-loai-tot-nghiep-thang-10-item/xep-loai-tot-nghiep-thang-10-item.component';
import { XepLoaiTotNghiepThang10Component } from './catalog/xep-loai-tot-nghiep-thang-10/xep-loai-tot-nghiep-thang-10/xep-loai-tot-nghiep-thang-10.component';
import { XepLoaiTotNghiepThang4ItemComponent } from './catalog/xep-loai-tot-nghiep-thang-4/xep-loai-tot-nghiep-thang-4-item/xep-loai-tot-nghiep-thang-4-item.component';
import { XepLoaiTotNghiepThang4Component } from './catalog/xep-loai-tot-nghiep-thang-4/xep-loai-tot-nghiep-thang-4/xep-loai-tot-nghiep-thang-4.component';
import { XuLyItemComponent } from './catalog/xu-ly/xu-ly-item/xu-ly-item.component';
import { XuLyComponent } from './catalog/xu-ly/xu-ly/xu-ly.component';
import { EmailLogComponent } from './email-log/email-log.component';
import { EmailTemplateItemComponent } from './email-template/email-template-item/email-template-item.component';
import { EmailTemplateComponent } from './email-template/email-template/email-template.component';
import { ForgotPasswordLogComponent } from './forgot-password-log/forgot-password-log.component';
import { RoleItemComponent } from './role/role-item/role-item.component';
import { RolePermissionComponent } from './role/role-permission/role-permission.component';
import { RoleUserComponent } from './role/role-user/role-user.component';
import { RoleComponent } from './role/role/role.component';
import { SystemLogComponent } from './system-log/system-log.component';
import { SystemRoutingModule } from './system-routing.module';
import { ThamSoHeThongItemComponent } from './tham-so-he-thong/tham-so-he-thong-item/tham-so-he-thong-item.component';
import { ThamSoHeThongComponent } from './tham-so-he-thong/tham-so-he-thong/tham-so-he-thong.component';
import { AddRoleComponent } from './user/add-role/add-role.component';
import { DanhSachLopQuanLyComponent } from './user/danh-sach-lop-quan-ly/danh-sach-lop-quan-ly.component';
import { GanLopQuanLyComponent } from './user/gan-lop-quan-ly/gan-lop-quan-ly.component';
import { UserItemComponent } from './user/user-item/user-item.component';
import { UserComponent } from './user/user/user.component';
import { WorkflowItemComponent } from './workflow/workflow-item/workflow-item.component';
import { WorkflowMapFunctionItemComponent } from './workflow/workflow-map-function-item/workflow-map-function-item.component';
import { WorkflowMapFunctionComponent } from './workflow/workflow-map-function/workflow-map-function.component';
import { WorkflowComponent } from './workflow/workflow/workflow.component';

@NgModule({
  declarations: [
    SystemLogComponent,
    UserComponent,
    AddRoleComponent,
    RoleComponent,
    RoleItemComponent,
    RoleUserComponent,
    RolePermissionComponent,
    UserItemComponent,
    EmailTemplateComponent,
    EmailTemplateItemComponent,
    ThamSoHeThongComponent,
    ThamSoHeThongItemComponent,
    HeDaoTaoComponent,
    HeDaoTaoItemComponent,
    TonGiaoComponent,
    TonGiaoItemComponent,
    QuocTichComponent,
    QuocTichItemComponent,
    DanTocComponent,
    DanTocItemComponent,
    GioiTinhComponent,
    GioiTinhItemComponent,
    HocViComponent,
    HocViItemComponent,
    HocHamComponent,
    HocHamItemComponent,
    KhoaComponent,
    KhoaItemComponent,
    NganhComponent,
    NganhItemComponent,
    ChuyenNganhComponent,
    ChuyenNganhItemComponent,
    TinhComponent,
    TinhItemComponent,
    HuyenComponent,
    HuyenItemComponent,
    KhuVucComponent,
    KhuVucItemComponent,
    NhomDoiTuongComponent,
    NhomDoiTuongItemComponent,
    DoiTuongComponent,
    DoiTuongItemComponent,
    DoiTuongHocBongComponent,
    DoiTuongHocBongItemComponent,
    CapKhenThuongKyLuatComponent,
    CapKhenThuongKyLuatItemComponent,
    LoaiKhenThuongComponent,
    LoaiKhenThuongItemComponent,
    HanhViComponent,
    HanhViItemComponent,
    XuLyComponent,
    XuLyItemComponent,
    ChucDanhComponent,
    ChucDanhItemComponent,
    XepLoaiRenLuyenComponent,
    XepLoaiRenLuyenItemComponent,
    LoaiRenLuyenComponent,
    LoaiRenLuyenItemComponent,
    LoaiThanhPhanDiemComponent,
    LoaiThanhPhanDiemComponent,
    XepLoaiHocTapThangDiem10ItemComponent,
    LoaiThanhPhanDiemComponent,
    XepHangHocLucComponent,
    XepHangHocLucItemComponent,
    HeDaoTaoComponent,
    HeDaoTaoItemComponent,
    XepLoaiHocLucThang4ItemComponent,
    XepLoaiHocLucThang4Component,
    XepLoaiTotNghiepThang4Component,
    XepLoaiTotNghiepThang4ItemComponent,
    XepLoaiTotNghiepThang10Component,
    XepLoaiTotNghiepThang10ItemComponent,
    LoaiChungChiComponent,
    LoaiChungChiItemComponent,
    XepLoaiChungChiItemComponent,
    XepLoaiChungChiComponent,
    NhomChungChiComponent,
    NhomChungChiItemComponent,
    XepLoaiHocTapThangDiem10Component,
    XepHangNamDaoTaoComponent,
    XepHangNamDaoTaoItemComponent,
    LoaiThanhPhanDiemItemComponent,
    LoaiGiayToComponent,
    LoaiGiayToItemComponent,
    HocKyDangKyComponent,
    HocKyDangKyItemComponent,
    PhuongNgoaiTruComponent,
    PhuongNgoaiTruItemComponent,
    HinhThucHocComponent,
    HinhThucHocItemComponent,
    XaComponent,
    XaItemComponent,
    XepLoaiHocBongComponent,
    XepLoaiHocBongItemComponent,
    ToaNhaComponent,
    ToaNhaItemComponent,
    DoiTuongHocPhiComponent,
    DoiTuongHocPhiItemComponent,
    BoMonComponent,
    BoMonItemComponent,
    CoSoDaoTaoComponent,
    CoSoDaoTaoItemComponent,
    DiemRenLuyenQuyDoiComponent,
    DiemRenLuyenQuyDoiItemComponent,
    DonViThucTapComponent,
    DonViThucTapItemComponent,
    LoaiQuyetDinhComponent,
    LoaiQuyetDinhItemComponent,
    HinhThucThiComponent,
    HinhThucThiItemComponent,
    PhongBanComponent,
    PhongBanItemComponent,
    KhoiKienThucComponent,
    KhoiKienThucItemComponent,
    DiemQuyDoiComponent,
    DiemQuyDoiItemComponent,
    ThanhPhanMonTheoHeComponent,
    ThanhPhanMonTheoHeItemComponent,
    LoaiThuChiComponent,
    LoaiThuChiItemComponent,
    PhongHocComponent,
    PhongHocItemComponent,
    CapRenLuyenComponent,
    CapRenLuyenItemComponent,
    TangComponent,
    TangItemComponent,
    LoaiPhongComponent,
    LoaiPhongItemComponent,
    EmailLogComponent,
    ForgotPasswordLogComponent,
    ChiTieuTuyenSinhComponent,
    ChiTieuTuyenSinhItemComponent,
    DanhSachLopQuanLyComponent,
    GanLopQuanLyComponent,
    BenhVienComponent,
    BenhVienItemComponent,
    PhuongAnComponent,
    PhuongAnItemComponent,
    PhuongThucDongComponent,
    PhuongThucDongItemComponent,
    VungComponent,
    VungItemComponent,
    MucHuongBhytComponent,
    MucHuongBhytItemComponent,
    WorkflowComponent,
    WorkflowItemComponent,
    WorkflowMapFunctionComponent,
    WorkflowMapFunctionItemComponent
  ],
  imports: [SharedModule, SystemRoutingModule, AgGridModule, CKEditorModule, WorkflowDesignerModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class SystenModule {}
